import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/class_provider.dart';
import '../providers/student_provider.dart';
import '../models/class_model.dart';
import '../models/educational_level.dart';
import '../widgets/class_form_dialog.dart';

class ClassesScreen extends StatefulWidget {
  const ClassesScreen({Key? key}) : super(key: key);

  @override
  State<ClassesScreen> createState() => _ClassesScreenState();
}

class _ClassesScreenState extends State<ClassesScreen> {
  String _selectedCycle = 'Tous';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ClassProvider>().loadClasses();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Classes'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        automaticallyImplyLeading: false,
      ),
      body: Column(
        children: [
          _buildFilterBar(),
          Expanded(
            child: Consumer<ClassProvider>(
              builder: (context, classProvider, child) {
                if (classProvider.isLoading) {
                  return const Center(child: CircularProgressIndicator());
                }

                final filteredClasses = _getFilteredClasses(classProvider.classes);

                if (filteredClasses.isEmpty) {
                  return _buildEmptyState();
                }

                return ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: filteredClasses.length,
                  itemBuilder: (context, index) {
                    final classModel = filteredClasses[index];
                    return _buildClassCard(classModel);
                  },
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showClassDialog(),
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildFilterBar() {
    final cycles = ['Tous', ...EducationalLevel.getAllCycles()];
    
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          const Text('Filtrer par cycle: '),
          const SizedBox(width: 8),
          Expanded(
            child: DropdownButton<String>(
              value: _selectedCycle,
              isExpanded: true,
              items: cycles.map((cycle) {
                return DropdownMenuItem(
                  value: cycle,
                  child: Text(cycle),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedCycle = value ?? 'Tous';
                });
              },
            ),
          ),
        ],
      ),
    );
  }

  List<ClassModel> _getFilteredClasses(List<ClassModel> classes) {
    if (_selectedCycle == 'Tous') {
      return classes;
    }
    return classes.where((c) => c.level.cycle == _selectedCycle).toList();
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.class_,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Aucune classe trouvée',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Appuyez sur + pour créer votre première classe',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildClassCard(ClassModel classModel) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getCycleColor(classModel.level.cycle),
          child: Text(
            classModel.level.code,
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Text(
          classModel.name,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('${classModel.level.fullName} - ${classModel.schoolYear}'),
            if (classModel.description != null)
              Text(
                classModel.description!,
                style: TextStyle(color: Colors.grey[600]),
              ),
            Consumer<StudentProvider>(
              builder: (context, studentProvider, child) {
                final studentCount = studentProvider.getStudentCountByClass(classModel.id!);
                return Text(
                  '$studentCount/${classModel.maxStudents} élèves',
                  style: TextStyle(
                    color: studentCount >= classModel.maxStudents 
                        ? Colors.red 
                        : Colors.grey[600],
                  ),
                );
              },
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'edit':
                _showClassDialog(classModel: classModel);
                break;
              case 'delete':
                _showDeleteDialog(classModel);
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: ListTile(
                leading: Icon(Icons.edit),
                title: Text('Modifier'),
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: ListTile(
                leading: Icon(Icons.delete),
                title: Text('Supprimer'),
              ),
            ),
          ],
        ),
        onTap: () => _viewClassDetails(classModel),
      ),
    );
  }

  Color _getCycleColor(String cycle) {
    switch (cycle) {
      case 'primaire':
        return Colors.green;
      case 'collège':
        return Colors.blue;
      case 'lycée':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  void _showClassDialog({ClassModel? classModel}) {
    showDialog(
      context: context,
      builder: (context) => ClassFormDialog(classModel: classModel),
    );
  }

  void _showDeleteDialog(ClassModel classModel) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Supprimer la classe'),
        content: Text('Êtes-vous sûr de vouloir supprimer la classe "${classModel.name}" ?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Annuler'),
          ),
          TextButton(
            onPressed: () {
              context.read<ClassProvider>().deleteClass(classModel.id!);
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Classe supprimée')),
              );
            },
            child: const Text('Supprimer'),
          ),
        ],
      ),
    );
  }

  void _viewClassDetails(ClassModel classModel) {
    // Navigate to class details screen
    // This will be implemented later
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Détails de la classe ${classModel.name}')),
    );
  }
}
