# Gestion de Classe - Application Flutter

Une application mobile Flutter pour la gestion de classes dans le système éducatif français (primaire, collège, lycée).

## Fonctionnalités

### ✅ Implémentées
- **Gestion des classes** : Création, modification et suppression de classes
- **Système éducatif français** : Support complet des niveaux (CP, CE1, CE2, CM1, CM2, 6ème, 5ème, 4ème, 3ème, 2nde, 1ère, Terminale)
- **Base de données locale** : Stockage offline avec SQLite
- **Interface utilisateur** : Design Material avec navigation par onglets
- **Filtrage** : Filtrage des classes par cycle (primaire, collège, lycée)

### 🚧 En cours de développement
- **Gestion des élèves** : Ajout, modification et suppression d'élèves
- **Gestion des matières** : Matières par défaut selon le niveau
- **Gestion des notes** : Système de notation français (0-20)
- **Rapports et statistiques** : Bulletins, moyennes, analyses

## Structure du projet

```
lib/
├── main.dart                 # Point d'entrée de l'application
├── models/                   # Modèles de données
│   ├── educational_level.dart
│   ├── class_model.dart
│   ├── student.dart
│   ├── subject.dart
│   └── mark.dart
├── services/                 # Services (base de données)
│   └── database_service.dart
├── providers/                # Gestion d'état avec Provider
│   ├── class_provider.dart
│   ├── student_provider.dart
│   ├── subject_provider.dart
│   └── mark_provider.dart
├── screens/                  # Écrans de l'application
│   ├── home_screen.dart
│   ├── classes_screen.dart
│   ├── students_screen.dart
│   ├── subjects_screen.dart
│   ├── marks_screen.dart
│   └── reports_screen.dart
└── widgets/                  # Composants réutilisables
    └── class_form_dialog.dart
```

## Modèles de données

### Niveaux éducatifs
- **Primaire** : CP, CE1, CE2, CM1, CM2
- **Collège** : 6ème, 5ème, 4ème, 3ème
- **Lycée** : 2nde, 1ère, Terminale

### Classes
- Nom de la classe
- Niveau éducatif
- Année scolaire
- Nombre maximum d'élèves
- Description (optionnel)

### Élèves
- Prénom et nom
- Date de naissance
- Niveau éducatif
- Classe assignée
- Notes (optionnel)

### Matières
- Nom de la matière
- Code (ex: MATH, FRAN)
- Niveau éducatif
- Coefficient
- Couleur pour l'interface
- Matières par défaut selon le cycle

### Notes
- Valeur (sur 20)
- Type (Évaluation, Devoir, Contrôle, Examen, Oral, Projet)
- Date
- Titre et description
- Élève et matière associés

## Installation et utilisation

### Prérequis
- Flutter SDK (version 3.0.0 ou supérieure)
- Android Studio ou VS Code
- Émulateur Android ou appareil physique

### Installation
1. Clonez le projet
2. Installez les dépendances :
   ```bash
   flutter pub get
   ```
3. Lancez l'application :
   ```bash
   flutter run
   ```

### Dépendances principales
- `sqflite` : Base de données SQLite locale
- `provider` : Gestion d'état
- `intl` : Internationalisation et formatage
- `path` : Gestion des chemins de fichiers
- `shared_preferences` : Stockage de préférences

## Fonctionnalités offline

L'application fonctionne entièrement hors ligne grâce à :
- Base de données SQLite locale
- Stockage de toutes les données sur l'appareil
- Pas de connexion internet requise

## Système de notation français

- Échelle de 0 à 20
- Appréciations automatiques :
  - 18-20 : Très bien
  - 16-18 : Bien
  - 14-16 : Assez bien
  - 12-14 : Passable
  - 10-12 : Insuffisant
  - 0-10 : Très insuffisant

## Prochaines étapes

1. **Compléter la gestion des élèves**
   - Formulaire d'ajout/modification
   - Détails des élèves
   - Photos d'élèves

2. **Finaliser la gestion des matières**
   - Initialisation automatique des matières par défaut
   - Personnalisation des matières

3. **Système de notes complet**
   - Saisie de notes
   - Calcul de moyennes
   - Graphiques de progression

4. **Rapports et exports**
   - Bulletins de notes
   - Export PDF
   - Statistiques de classe

5. **Fonctionnalités avancées**
   - Gestion des absences
   - Calendrier scolaire
   - Sauvegarde/restauration

## Contribution

Ce projet est conçu pour les enseignants du système éducatif français. Les contributions sont les bienvenues pour améliorer l'application.

## Licence

Ce projet est sous licence MIT.
