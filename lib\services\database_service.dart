import 'dart:async';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/class_model.dart';
import '../models/student.dart';
import '../models/subject.dart';
import '../models/mark.dart';

class DatabaseService {
  static Database? _database;
  static const String _databaseName = 'gestion_classe.db';
  static const int _databaseVersion = 1;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), _databaseName);
    return await openDatabase(
      path,
      version: _databaseVersion,
      onCreate: _onCreate,
    );
  }

  Future<void> initDatabase() async {
    await database;
  }

  Future<void> _onCreate(Database db, int version) async {
    // Create classes table
    await db.execute('''
      CREATE TABLE classes (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        level TEXT NOT NULL,
        schoolYear TEXT NOT NULL,
        description TEXT,
        maxStudents INTEGER DEFAULT 30,
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL
      )
    ''');

    // Create students table
    await db.execute('''
      CREATE TABLE students (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        firstName TEXT NOT NULL,
        lastName TEXT NOT NULL,
        dateOfBirth TEXT NOT NULL,
        level TEXT NOT NULL,
        classId INTEGER NOT NULL,
        notes TEXT,
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL,
        FOREIGN KEY (classId) REFERENCES classes (id) ON DELETE CASCADE
      )
    ''');

    // Create subjects table
    await db.execute('''
      CREATE TABLE subjects (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        code TEXT NOT NULL,
        level TEXT NOT NULL,
        description TEXT,
        coefficient INTEGER DEFAULT 1,
        color TEXT DEFAULT '#2196F3',
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL
      )
    ''');

    // Create marks table
    await db.execute('''
      CREATE TABLE marks (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        studentId INTEGER NOT NULL,
        subjectId INTEGER NOT NULL,
        value REAL NOT NULL,
        maxValue REAL DEFAULT 20.0,
        type TEXT NOT NULL,
        title TEXT,
        description TEXT,
        date TEXT NOT NULL,
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL,
        FOREIGN KEY (studentId) REFERENCES students (id) ON DELETE CASCADE,
        FOREIGN KEY (subjectId) REFERENCES subjects (id) ON DELETE CASCADE
      )
    ''');

    // Create indexes for better performance
    await db.execute('CREATE INDEX idx_students_classId ON students (classId)');
    await db.execute('CREATE INDEX idx_marks_studentId ON marks (studentId)');
    await db.execute('CREATE INDEX idx_marks_subjectId ON marks (subjectId)');
    await db.execute('CREATE INDEX idx_subjects_level ON subjects (level)');
  }

  // Classes CRUD operations
  Future<int> insertClass(ClassModel classModel) async {
    final db = await database;
    return await db.insert('classes', classModel.toMap());
  }

  Future<List<ClassModel>> getClasses() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('classes', orderBy: 'name ASC');
    return List.generate(maps.length, (i) => ClassModel.fromMap(maps[i]));
  }

  Future<ClassModel?> getClass(int id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'classes',
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return ClassModel.fromMap(maps.first);
    }
    return null;
  }

  Future<int> updateClass(ClassModel classModel) async {
    final db = await database;
    return await db.update(
      'classes',
      classModel.toMap(),
      where: 'id = ?',
      whereArgs: [classModel.id],
    );
  }

  Future<int> deleteClass(int id) async {
    final db = await database;
    return await db.delete(
      'classes',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Students CRUD operations
  Future<int> insertStudent(Student student) async {
    final db = await database;
    return await db.insert('students', student.toMap());
  }

  Future<List<Student>> getStudents({int? classId}) async {
    final db = await database;
    List<Map<String, dynamic>> maps;
    
    if (classId != null) {
      maps = await db.query(
        'students',
        where: 'classId = ?',
        whereArgs: [classId],
        orderBy: 'lastName ASC, firstName ASC',
      );
    } else {
      maps = await db.query('students', orderBy: 'lastName ASC, firstName ASC');
    }
    
    return List.generate(maps.length, (i) => Student.fromMap(maps[i]));
  }

  Future<Student?> getStudent(int id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'students',
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return Student.fromMap(maps.first);
    }
    return null;
  }

  Future<int> updateStudent(Student student) async {
    final db = await database;
    return await db.update(
      'students',
      student.toMap(),
      where: 'id = ?',
      whereArgs: [student.id],
    );
  }

  Future<int> deleteStudent(int id) async {
    final db = await database;
    return await db.delete(
      'students',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Subjects CRUD operations
  Future<int> insertSubject(Subject subject) async {
    final db = await database;
    return await db.insert('subjects', subject.toMap());
  }

  Future<List<Subject>> getSubjects({String? level}) async {
    final db = await database;
    List<Map<String, dynamic>> maps;

    if (level != null) {
      maps = await db.query(
        'subjects',
        where: 'level = ?',
        whereArgs: [level],
        orderBy: 'name ASC',
      );
    } else {
      maps = await db.query('subjects', orderBy: 'name ASC');
    }

    return List.generate(maps.length, (i) => Subject.fromMap(maps[i]));
  }

  Future<Subject?> getSubject(int id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'subjects',
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return Subject.fromMap(maps.first);
    }
    return null;
  }

  Future<int> updateSubject(Subject subject) async {
    final db = await database;
    return await db.update(
      'subjects',
      subject.toMap(),
      where: 'id = ?',
      whereArgs: [subject.id],
    );
  }

  Future<int> deleteSubject(int id) async {
    final db = await database;
    return await db.delete(
      'subjects',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Marks CRUD operations
  Future<int> insertMark(Mark mark) async {
    final db = await database;
    return await db.insert('marks', mark.toMap());
  }

  Future<List<Mark>> getMarks({int? studentId, int? subjectId}) async {
    final db = await database;
    List<Map<String, dynamic>> maps;

    if (studentId != null && subjectId != null) {
      maps = await db.query(
        'marks',
        where: 'studentId = ? AND subjectId = ?',
        whereArgs: [studentId, subjectId],
        orderBy: 'date DESC',
      );
    } else if (studentId != null) {
      maps = await db.query(
        'marks',
        where: 'studentId = ?',
        whereArgs: [studentId],
        orderBy: 'date DESC',
      );
    } else if (subjectId != null) {
      maps = await db.query(
        'marks',
        where: 'subjectId = ?',
        whereArgs: [subjectId],
        orderBy: 'date DESC',
      );
    } else {
      maps = await db.query('marks', orderBy: 'date DESC');
    }

    return List.generate(maps.length, (i) => Mark.fromMap(maps[i]));
  }

  Future<Mark?> getMark(int id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'marks',
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return Mark.fromMap(maps.first);
    }
    return null;
  }

  Future<int> updateMark(Mark mark) async {
    final db = await database;
    return await db.update(
      'marks',
      mark.toMap(),
      where: 'id = ?',
      whereArgs: [mark.id],
    );
  }

  Future<int> deleteMark(int id) async {
    final db = await database;
    return await db.delete(
      'marks',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Statistics and analytics
  Future<double> getStudentAverage(int studentId, {int? subjectId}) async {
    final db = await database;
    String query = '''
      SELECT AVG(value * 20.0 / maxValue) as average
      FROM marks
      WHERE studentId = ?
    ''';
    List<dynamic> args = [studentId];

    if (subjectId != null) {
      query += ' AND subjectId = ?';
      args.add(subjectId);
    }

    final result = await db.rawQuery(query, args);
    return result.first['average'] as double? ?? 0.0;
  }

  Future<double> getClassAverage(int classId, {int? subjectId}) async {
    final db = await database;
    String query = '''
      SELECT AVG(m.value * 20.0 / m.maxValue) as average
      FROM marks m
      INNER JOIN students s ON m.studentId = s.id
      WHERE s.classId = ?
    ''';
    List<dynamic> args = [classId];

    if (subjectId != null) {
      query += ' AND m.subjectId = ?';
      args.add(subjectId);
    }

    final result = await db.rawQuery(query, args);
    return result.first['average'] as double? ?? 0.0;
  }

  Future<void> close() async {
    final db = await database;
    await db.close();
  }
}
